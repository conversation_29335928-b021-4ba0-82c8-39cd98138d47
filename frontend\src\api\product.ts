import { api } from './index'

// 商品相關接口類型定義
export interface Product {
  id: number
  name: string
  description?: string
  categoryId: number
  price: number
  originalPrice?: number
  stock: number
  soldCount: number
  status: number
  mainImageUrl?: string
  weight?: number
  brand?: string
  model?: string
  tags?: string
  isRecommended: number
  isHot: number
  sortOrder: number
  createdAt: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
  category?: ProductCategory
  images?: ProductImage[]
}

export interface ProductCategory {
  id: number
  name: string
  parentId: number
  level: number
  sortOrder: number
  status: number
  description?: string
  iconUrl?: string
  isLeaf: number
  createdAt: string
  updatedAt?: string
  createdBy?: number
  parent?: ProductCategory
  children?: ProductCategory[]
}

export interface ProductImage {
  id: number
  productId: number
  imageUrl: string
  sortOrder: number
  isMain: number
  description?: string
  fileSize?: number
  width?: number
  height?: number
  format?: string
  createdAt: string
  updatedAt?: string
  uploadedBy?: number
}

export interface PagedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  errorCode?: string
  timestamp?: string
}

// 商品分類API
export const productCategoryAPI = {
  // 獲取分類樹
  getCategoryTree: (): Promise<ApiResponse<ProductCategory[]>> =>
    api.get('/product/categories/tree'),

  // 獲取根分類
  getRootCategories: (): Promise<ApiResponse<ProductCategory[]>> =>
    api.get('/product/categories/roots'),

  // 獲取子分類
  getChildCategories: (parentId: number): Promise<ApiResponse<ProductCategory[]>> =>
    api.get(`/product/categories/children/${parentId}`),

  // 獲取分類詳情
  getCategoryById: (id: number): Promise<ApiResponse<ProductCategory>> =>
    api.get(`/product/categories/${id}`),

  // 獲取分類路徑
  getCategoryPath: (id: number): Promise<ApiResponse<string>> =>
    api.get(`/product/categories/${id}/path`),

  // 獲取葉子分類
  getLeafCategories: (): Promise<ApiResponse<ProductCategory[]>> =>
    api.get('/product/categories/leaves'),

  // 創建分類
  createCategory: (category: Partial<ProductCategory>): Promise<ApiResponse<ProductCategory>> =>
    api.post('/product/categories', category),

  // 更新分類
  updateCategory: (id: number, category: Partial<ProductCategory>): Promise<ApiResponse<ProductCategory>> =>
    api.put(`/product/categories/${id}`, category),

  // 刪除分類
  deleteCategory: (id: number): Promise<ApiResponse<string>> =>
    api.delete(`/product/categories/${id}`),

  // 批量刪除分類
  batchDeleteCategories: (ids: number[]): Promise<ApiResponse<string>> =>
    api.delete('/product/categories/batch', { data: ids }),

  // 切換分類狀態
  toggleCategoryStatus: (id: number, status: number): Promise<ApiResponse<string>> =>
    api.put(`/product/categories/${id}/status?status=${status}`),

  // 調整分類排序
  updateCategorySort: (id: number, sortOrder: number): Promise<ApiResponse<string>> =>
    api.put(`/product/categories/${id}/sort?sortOrder=${sortOrder}`),

  // 移動分類
  moveCategory: (id: number, newParentId: number): Promise<ApiResponse<string>> =>
    api.put(`/product/categories/${id}/move?newParentId=${newParentId}`),

  // 分頁查詢分類
  getCategoriesWithFilters: (params: {
    name?: string
    parentId?: number
    status?: number
    page?: number
    size?: number
  }): Promise<ApiResponse<PagedResponse<ProductCategory>>> => {
    const queryParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })
    return api.get(`/product/categories/admin/list?${queryParams}`)
  },

  // 統計分類商品數量
  getProductCountByCategory: (id: number): Promise<ApiResponse<number>> =>
    api.get(`/product/categories/${id}/product-count`),

  // 檢查分類名稱
  checkCategoryNameExists: (name: string, parentId: number, excludeId?: number): Promise<ApiResponse<boolean>> => {
    const params = new URLSearchParams({ name, parentId: parentId.toString() })
    if (excludeId) params.append('excludeId', excludeId.toString())
    return api.get(`/product/categories/check-name?${params}`)
  },

  // 獲取分類統計
  getCategoryStatistics: (): Promise<ApiResponse<any>> =>
    api.get('/product/categories/statistics'),

  // 刷新緩存
  refreshCategoryCache: (): Promise<ApiResponse<string>> =>
    api.post('/product/categories/cache/refresh'),

  // 重建分類樹
  rebuildCategoryTree: (): Promise<ApiResponse<string>> =>
    api.post('/product/categories/rebuild')
}

// 商品API
export const productAPI = {
  // 分頁查詢商品
  getProducts: (page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products?page=${page}&size=${size}`),

  // 根據分類查詢商品
  getProductsByCategory: (categoryId: number, page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products/category/${categoryId}?page=${page}&size=${size}`),

  // 獲取商品詳情
  getProductById: (id: number): Promise<ApiResponse<Product>> =>
    api.get(`/products/${id}`),

  // 搜索商品
  searchProducts: (keyword: string, page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`),

  // 多條件查詢商品
  searchProductsWithFilters: (params: {
    name?: string
    categoryId?: number
    status?: number
    minPrice?: number
    maxPrice?: number
    brand?: string
    isRecommended?: number
    isHot?: number
    page?: number
    size?: number
  }): Promise<ApiResponse<PagedResponse<Product>>> => {
    const queryParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })
    return api.get(`/products/filter?${queryParams}`)
  },

  // 獲取推薦商品
  getRecommendedProducts: (page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products/recommended?page=${page}&size=${size}`),

  // 獲取熱門商品
  getHotProducts: (page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products/hot?page=${page}&size=${size}`),

  // 獲取最新商品
  getLatestProducts: (page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products/latest?page=${page}&size=${size}`),

  // 獲取熱銷商品
  getBestSellingProducts: (page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products/best-selling?page=${page}&size=${size}`),

  // 按價格排序商品
  getProductsByPrice: (ascending = true, page = 0, size = 20): Promise<ApiResponse<PagedResponse<Product>>> =>
    api.get(`/products/price-sorted?ascending=${ascending}&page=${page}&size=${size}`),

  // 獲取相關商品
  getRelatedProducts: (id: number, limit = 10): Promise<ApiResponse<Product[]>> =>
    api.get(`/products/${id}/related?limit=${limit}`),

  // 創建商品
  createProduct: (product: Partial<Product>): Promise<ApiResponse<Product>> =>
    api.post('/products', product),

  // 更新商品
  updateProduct: (id: number, product: Partial<Product>): Promise<ApiResponse<Product>> =>
    api.put(`/products/${id}`, product),

  // 刪除商品
  deleteProduct: (id: number): Promise<ApiResponse<string>> =>
    api.delete(`/products/${id}`),

  // 批量刪除商品
  batchDeleteProducts: (ids: number[]): Promise<ApiResponse<string>> =>
    api.delete('/products/batch', { data: ids }),

  // 切換商品狀態
  toggleProductStatus: (id: number, status: number): Promise<ApiResponse<string>> =>
    api.put(`/products/${id}/status?status=${status}`),

  // 設置推薦商品
  setRecommended: (id: number, isRecommended: number): Promise<ApiResponse<string>> =>
    api.put(`/products/${id}/recommended?isRecommended=${isRecommended}`),

  // 設置熱門商品
  setHot: (id: number, isHot: number): Promise<ApiResponse<string>> =>
    api.put(`/products/${id}/hot?isHot=${isHot}`),

  // 獲取瀏覽次數
  getViewCount: (id: number): Promise<ApiResponse<number>> =>
    api.get(`/products/${id}/view-count`),

  // 檢查商品名稱
  checkProductNameExists: (name: string, excludeId?: number): Promise<ApiResponse<boolean>> => {
    const params = new URLSearchParams({ name })
    if (excludeId) params.append('excludeId', excludeId.toString())
    return api.get(`/products/check-name?${params}`)
  },

  // 獲取商品統計
  getProductStatistics: (): Promise<ApiResponse<any>> =>
    api.get('/products/statistics'),

  // 獲取狀態統計
  getProductStatusStatistics: (): Promise<ApiResponse<any>> =>
    api.get('/products/status-statistics')
}
