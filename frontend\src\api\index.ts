import axios from 'axios'

// API響應類型定義
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data: T
}

// 創建 axios 實例
const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 請求攔截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 用於防止多個請求同時觸發刷新
let isRefreshing = false
let failedQueue: Array<{
  resolve: (value?: any) => void
  reject: (reason?: any) => void
}> = []

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// 響應攔截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    const originalRequest = error.config

    // 處理 401 和 403 錯誤（token 過期或無效）
    if ((error.response?.status === 401 || error.response?.status === 403) && !originalRequest._retry) {
      if (isRefreshing) {
        // 如果正在刷新，將請求加入隊列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return api(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      try {
        // 嘗試刷新 token
        const refreshToken = localStorage.getItem('refreshToken')
        if (refreshToken) {
          const response = await api.post('/auth/refresh', { refreshToken })
          if (response.success) {
            const newToken = response.data.accessToken
            localStorage.setItem('token', newToken)

            // 更新默認請求頭
            api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
            originalRequest.headers.Authorization = `Bearer ${newToken}`

            processQueue(null, newToken)

            // 重試原始請求
            return api(originalRequest)
          }
        }

        // 刷新失敗，跳轉到登入頁
        throw new Error('Token refresh failed')

      } catch (refreshError) {
        processQueue(refreshError, null)

        // 清理所有 token 和用戶信息
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('user')

        // 跳轉到登入頁
        window.location.href = '/login'

        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }

    return Promise.reject(error.response?.data || error.message)
  }
)

// API 接口定義
export const authAPI = {
  // 登入
  login: (data: { username: string; password: string }): Promise<ApiResponse<{accessToken: string, refreshToken: string, user: any}>> =>
    api.post('/auth/login', data),

  // 註冊
  register: (data: { username: string; email: string; password: string; verificationCode: string }): Promise<ApiResponse<{accessToken: string, refreshToken: string, user: any}>> =>
    api.post('/auth/register', data),
  
  // 發送驗證碼
  sendVerificationCode: (data: { email: string; type: string }): Promise<ApiResponse> =>
    api.post('/auth/send-verification-code', data),
  
  // 檢查用戶名
  checkUsername: (username: string): Promise<ApiResponse<{available: boolean}>> =>
    api.get(`/auth/check-username?username=${username}`),
  
  // 檢查郵箱
  checkEmail: (email: string): Promise<ApiResponse<{available: boolean}>> =>
    api.get(`/auth/check-email?email=${email}`),

  // 刷新 token
  refreshToken: (data: { refreshToken: string }): Promise<ApiResponse<{accessToken: string}>> =>
    api.post('/auth/refresh', data)
}

export const userAPI = {
  // 獲取用戶信息
  getProfile: (): Promise<ApiResponse<any>> => api.get('/user/profile'),
  
  // 更新用戶信息
  updateProfile: (data: any): Promise<ApiResponse<any>> => api.put('/user/profile', data),
  
  // 獲取用戶統計
  getStats: (): Promise<ApiResponse<any>> => api.get('/user/stats')
}

export const identityAPI = {
  // 提交身份認證
  submitVerification: (formData: FormData): Promise<ApiResponse<any>> =>
    api.post('/identity/submit', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  // 獲取我的認證記錄
  getMyVerifications: (): Promise<ApiResponse<any[]>> => api.get('/identity/my-verifications'),

  // 獲取待審核列表（管理員）
  getPendingVerifications: (): Promise<ApiResponse<any[]>> => api.get('/identity/pending'),

  // 審核身份認證（管理員）
  reviewVerification: (id: number, approved: boolean, comment?: string): Promise<ApiResponse<string>> =>
    api.post(`/identity/review/${id}`, null, {
      params: { approved, comment }
    }),

  // 獲取認證詳情（管理員）
  getVerificationDetail: (id: number): Promise<ApiResponse<any>> => api.get(`/identity/${id}`)
}

// 關注功能API
export const followAPI = {
  // 關注用戶
  followUser: (userId: number): Promise<ApiResponse<any>> =>
    api.post(`/user/follow/${userId}`),

  // 取消關注用戶
  unfollowUser: (userId: number): Promise<ApiResponse<any>> =>
    api.delete(`/user/follow/${userId}`),

  // 檢查關注狀態
  getFollowStatus: (userId: number): Promise<ApiResponse<{isFollowing: boolean}>> =>
    api.get(`/user/follow/status/${userId}`),

  // 獲取關注列表
  getFollowingList: (page: number = 0, size: number = 20): Promise<ApiResponse<{data: any[], totalCount: number, page: number, size: number}>> =>
    api.get(`/user/following?page=${page}&size=${size}`),

  // 獲取粉絲列表
  getFollowersList: (page: number = 0, size: number = 20): Promise<ApiResponse<{data: any[], totalCount: number, page: number, size: number}>> =>
    api.get(`/user/followers?page=${page}&size=${size}`),

  // 獲取關注統計
  getFollowStats: (): Promise<ApiResponse<{followingCount: number, followersCount: number}>> =>
    api.get('/user/follow/stats'),

  // 推薦用戶
  getRecommendedUsers: (limit: number = 10): Promise<ApiResponse<{data: any[], count: number}>> =>
    api.get(`/user/recommend?limit=${limit}`),

  // 獲取共同關注
  getMutualFollows: (userId: number, page: number = 0, size: number = 20): Promise<ApiResponse<{data: any[], count: number, page: number, size: number}>> =>
    api.get(`/user/mutual-follows/${userId}?page=${page}&size=${size}`)
}

// 收藏功能API類型定義
export interface FavoriteDto {
  id: number
  userId: number
  itemId: number
  itemTitle: string
  itemDescription: string
  itemType: 'ARTICLE' | 'VIDEO' | 'IMAGE' | 'LINK' | 'OTHER'
  contentUrl: string
  thumbnailUrl: string
  createdAt: string
}

export interface FavoriteItemDto {
  id: number
  title: string
  description: string
  itemType: 'ARTICLE' | 'VIDEO' | 'IMAGE' | 'LINK' | 'OTHER'
  contentUrl: string
  thumbnailUrl: string
  favoriteCount: number
  createdAt: string
  isFavorited: boolean
}

export interface FavoriteStatsDto {
  itemId: number
  favoriteCount: number
  isFavorited: boolean
  lastFavoriteTime: string
}

export interface PagedResponse<T> {
  content: T[]
  page: number
  size: number
  totalElements: number
  totalPages: number
  first: boolean
  last: boolean
  empty: boolean
}

// 收藏功能API
export const favoriteAPI = {
  // 收藏內容
  addFavorite: (itemId: number): Promise<ApiResponse<string>> =>
    api.post(`/favorite/${itemId}`),

  // 取消收藏
  removeFavorite: (itemId: number): Promise<ApiResponse<string>> =>
    api.delete(`/favorite/${itemId}`),

  // 檢查收藏狀態
  getFavoriteStatus: (itemId: number): Promise<ApiResponse<boolean>> =>
    api.get(`/favorite/status/${itemId}`),

  // 獲取我的收藏
  getMyFavorites: (page: number = 0, size: number = 20, itemType?: string): Promise<ApiResponse<PagedResponse<FavoriteDto>>> =>
    api.get(`/favorite/my-favorites?page=${page}&size=${size}${itemType ? `&itemType=${itemType}` : ''}`),

  // 獲取收藏排行榜
  getFavoriteRanking: (page: number = 0, size: number = 20, itemType?: string): Promise<ApiResponse<PagedResponse<FavoriteItemDto>>> =>
    api.get(`/favorite/ranking?page=${page}&size=${size}${itemType ? `&itemType=${itemType}` : ''}`),

  // 獲取收藏統計
  getFavoriteStats: (itemId: number): Promise<ApiResponse<FavoriteStatsDto>> =>
    api.get(`/favorite/stats/${itemId}`),

  // 搜索可收藏內容
  searchFavoriteItems: (keyword: string, page: number = 0, size: number = 20): Promise<ApiResponse<PagedResponse<FavoriteItemDto>>> =>
    api.get(`/favorite/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`),

  // 獲取最近創建的內容
  getRecentItems: (page: number = 0, size: number = 20, itemType?: string): Promise<ApiResponse<PagedResponse<FavoriteItemDto>>> =>
    api.get(`/favorite/recent?page=${page}&size=${size}${itemType ? `&itemType=${itemType}` : ''}`)
}

export { api }
export default api
