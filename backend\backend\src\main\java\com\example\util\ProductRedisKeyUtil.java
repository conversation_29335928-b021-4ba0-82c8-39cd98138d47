package com.example.util;

/**
 * 商品Redis Key管理工具類
 * 統一管理所有商品相關的Redis Key
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
public class ProductRedisKeyUtil {
    
    /**
     * Key前綴
     */
    private static final String PRODUCT_PREFIX = "product:";
    private static final String CATEGORY_PREFIX = "product:category:";
    private static final String CACHE_PREFIX = "product:cache:";
    private static final String COUNTER_PREFIX = "product:counter:";
    
    /**
     * 商品分類相關Key
     */
    
    /**
     * 分類樹緩存Key
     * 格式：product:category:tree
     */
    public static String categoryTreeKey() {
        return CATEGORY_PREFIX + "tree";
    }
    
    /**
     * 分類下商品數量Key
     * 格式：product:category:count:{categoryId}
     */
    public static String categoryProductCountKey(Long categoryId) {
        return CATEGORY_PREFIX + "count:" + categoryId;
    }
    
    /**
     * 分類路徑緩存Key
     * 格式：product:category:path:{categoryId}
     */
    public static String categoryPathKey(Long categoryId) {
        return CATEGORY_PREFIX + "path:" + categoryId;
    }
    
    /**
     * 子分類列表Key
     * 格式：product:category:children:{parentId}
     */
    public static String categoryChildrenKey(Long parentId) {
        return CATEGORY_PREFIX + "children:" + parentId;
    }
    
    /**
     * 商品相關Key
     */
    
    /**
     * 商品詳情緩存Key
     * 格式：product:detail:{productId}
     */
    public static String productDetailKey(Long productId) {
        return PRODUCT_PREFIX + "detail:" + productId;
    }
    
    /**
     * 商品圖片列表Key
     * 格式：product:images:{productId}
     */
    public static String productImagesKey(Long productId) {
        return PRODUCT_PREFIX + "images:" + productId;
    }
    
    /**
     * 商品庫存Key
     * 格式：product:stock:{productId}
     */
    public static String productStockKey(Long productId) {
        return PRODUCT_PREFIX + "stock:" + productId;
    }
    
    /**
     * 商品銷量Key
     * 格式：product:sold:{productId}
     */
    public static String productSoldCountKey(Long productId) {
        return PRODUCT_PREFIX + "sold:" + productId;
    }
    
    /**
     * 商品列表緩存Key
     */
    
    /**
     * 分類商品列表Key
     * 格式：product:cache:category:{categoryId}:page:{page}:size:{size}
     */
    public static String categoryProductListKey(Long categoryId, int page, int size) {
        return CACHE_PREFIX + "category:" + categoryId + ":page:" + page + ":size:" + size;
    }
    
    /**
     * 熱門商品列表Key
     * 格式：product:cache:hot:page:{page}:size:{size}
     */
    public static String hotProductListKey(int page, int size) {
        return CACHE_PREFIX + "hot:page:" + page + ":size:" + size;
    }
    
    /**
     * 推薦商品列表Key
     * 格式：product:cache:recommended:page:{page}:size:{size}
     */
    public static String recommendedProductListKey(int page, int size) {
        return CACHE_PREFIX + "recommended:page:" + page + ":size:" + size;
    }
    
    /**
     * 最新商品列表Key
     * 格式：product:cache:latest:page:{page}:size:{size}
     */
    public static String latestProductListKey(int page, int size) {
        return CACHE_PREFIX + "latest:page:" + page + ":size:" + size;
    }
    
    /**
     * 搜索結果緩存Key
     * 格式：product:cache:search:{keyword}:page:{page}:size:{size}
     */
    public static String searchResultKey(String keyword, int page, int size) {
        // 對關鍵詞進行編碼，避免特殊字符
        String encodedKeyword = keyword.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_");
        return CACHE_PREFIX + "search:" + encodedKeyword + ":page:" + page + ":size:" + size;
    }
    
    /**
     * 多條件查詢結果Key
     * 格式：product:cache:filter:{hash}:page:{page}:size:{size}
     */
    public static String filterResultKey(String filterHash, int page, int size) {
        return CACHE_PREFIX + "filter:" + filterHash + ":page:" + page + ":size:" + size;
    }
    
    /**
     * 統計計數器Key
     */
    
    /**
     * 商品瀏覽次數Key
     * 格式：product:counter:view:{productId}
     */
    public static String productViewCountKey(Long productId) {
        return COUNTER_PREFIX + "view:" + productId;
    }
    
    /**
     * 商品收藏次數Key
     * 格式：product:counter:favorite:{productId}
     */
    public static String productFavoriteCountKey(Long productId) {
        return COUNTER_PREFIX + "favorite:" + productId;
    }
    
    /**
     * 每日商品瀏覽統計Key
     * 格式：product:counter:daily:view:{date}
     */
    public static String dailyViewCountKey(String date) {
        return COUNTER_PREFIX + "daily:view:" + date;
    }
    
    /**
     * 批量操作Key模式
     */
    
    /**
     * 所有商品詳情緩存Key模式
     * 格式：product:detail:*
     */
    public static String allProductDetailPattern() {
        return PRODUCT_PREFIX + "detail:*";
    }
    
    /**
     * 所有分類相關緩存Key模式
     * 格式：product:category:*
     */
    public static String allCategoryPattern() {
        return CATEGORY_PREFIX + "*";
    }
    
    /**
     * 所有商品列表緩存Key模式
     * 格式：product:cache:*
     */
    public static String allProductCachePattern() {
        return CACHE_PREFIX + "*";
    }
    
    /**
     * 指定分類的所有緩存Key模式
     * 格式：product:cache:category:{categoryId}:*
     */
    public static String categoryProductCachePattern(Long categoryId) {
        return CACHE_PREFIX + "category:" + categoryId + ":*";
    }
    
    /**
     * 指定商品的所有緩存Key模式
     * 格式：product:*:{productId}*
     */
    public static String productAllCachePattern(Long productId) {
        return PRODUCT_PREFIX + "*:" + productId + "*";
    }
    
    /**
     * 工具方法
     */
    
    /**
     * 生成過濾條件的哈希值（用於緩存Key）
     */
    public static String generateFilterHash(Object... params) {
        StringBuilder sb = new StringBuilder();
        for (Object param : params) {
            if (param != null) {
                sb.append(param.toString()).append("_");
            } else {
                sb.append("null_");
            }
        }
        return String.valueOf(sb.toString().hashCode());
    }
}
