import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'
import { useAdminStore } from '../stores/admin'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/app/home'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/Login.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('../views/Register.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/app',
      component: () => import('../components/layout/MainLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          redirect: '/app/home'
        },
        {
          path: 'home',
          name: 'Home',
          component: () => import('../views/Home.vue')
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('../views/Profile.vue')
        },
        {
          path: 'identity',
          name: 'Identity',
          component: () => import('../views/Identity.vue')
        },
        {
          path: 'follow',
          name: 'Follow',
          component: () => import('../views/FollowPage.vue')
        },
        {
          path: 'my-favorites',
          name: 'MyFavorites',
          component: () => import('../views/MyFavorites.vue')
        },
        {
          path: 'products',
          name: 'Products',
          component: () => import('../views/ProductsView.vue')
        },
        {
          path: 'products/categories',
          name: 'ProductCategories',
          component: () => import('../views/ProductsView.vue')
        },
        {
          path: 'products/search',
          name: 'ProductSearch',
          component: () => import('../views/ProductsView.vue')
        },
        {
          path: 'products/hot',
          name: 'HotProducts',
          component: () => import('../views/ProductsView.vue')
        },
        {
          path: 'products/recommended',
          name: 'RecommendedProducts',
          component: () => import('../views/ProductsView.vue')
        },
        {
          path: 'products/:id',
          name: 'ProductDetail',
          component: () => import('../views/ProductDetailView.vue')
        },
        {
          path: 'cart',
          name: 'Cart',
          component: () => import('../views/CartView.vue')
        },
        {
          path: 'checkout',
          name: 'Checkout',
          component: () => import('../views/CheckoutView.vue')
        },
        {
          path: 'orders',
          name: 'Orders',
          component: () => import('../views/OrdersView.vue')
        },
        {
          path: 'payment/:orderId',
          name: 'Payment',
          component: () => import('../views/PaymentView.vue')
        }
      ]
    },
    {
      path: '/favorite-ranking',
      name: 'FavoriteRanking',
      component: () => import('../views/FavoriteRanking.vue')
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('../views/Admin.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin-login',
      name: 'AdminLogin',
      component: () => import('../views/AdminLogin.vue'),
      meta: { requiresAdminGuest: true }
    },
    {
      path: '/admin-dashboard',
      name: 'AdminDashboard',
      component: () => import('../views/AdminDashboard.vue'),
      meta: { requiresAdminAuth: true }
    },
    // Catch-all 路由，处理未匹配的路径
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: '/app/home'
    }
  ]
})

// 路由守衛
router.beforeEach((to, from, next) => {
  console.log('路由导航:', { from: from.path, to: to.path })
  
  const userStore = useUserStore()
  const adminStore = useAdminStore()

  // 初始化用戶狀態
  if (!userStore.user && userStore.token) {
    userStore.initializeUser()
  }

  // 初始化管理員狀態
  if (!adminStore.admin && adminStore.token) {
    adminStore.initializeFromStorage()
  }

  // 檢查管理員認證
  if (to.meta.requiresAdminAuth && !adminStore.isLoggedIn) {
    next('/admin-login')
    return
  }

  // 檢查管理員訪客狀態
  if (to.meta.requiresAdminGuest && adminStore.isLoggedIn) {
    next('/admin-dashboard')
    return
  }

  // 檢查是否需要登入
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
    return
  }

  // 檢查是否需要管理員權限
  if (to.meta.requiresAdmin && !userStore.isAdmin) {
    next('/app/home')
    return
  }

  // 檢查是否需要訪客狀態（未登入）
  if (to.meta.requiresGuest && userStore.isLoggedIn) {
    next('/app/home')
    return
  }

  next()
})

export default router
