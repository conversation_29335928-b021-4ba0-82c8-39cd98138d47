<template>
  <div class="checkout-view">
    <div class="container">
      <h1 class="page-title">確認訂單</h1>
      
      <div class="checkout-content">
        <!-- 收貨地址 -->
        <div class="section address-section">
          <h2 class="section-title">收貨地址</h2>
          <div class="address-form">
            <div class="form-row">
              <div class="form-group">
                <label>收貨人姓名 *</label>
                <input 
                  type="text" 
                  v-model="orderForm.receiverName" 
                  placeholder="請輸入收貨人姓名"
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <label>聯繫電話 *</label>
                <input 
                  type="tel" 
                  v-model="orderForm.receiverPhone" 
                  placeholder="請輸入聯繫電話"
                  class="form-control"
                />
              </div>
            </div>
            <div class="form-group">
              <label>詳細地址 *</label>
              <textarea 
                v-model="orderForm.receiverAddress" 
                placeholder="請輸入詳細收貨地址"
                class="form-control"
                rows="3"
              ></textarea>
            </div>
            <div class="form-group">
              <label>訂單備註</label>
              <textarea 
                v-model="orderForm.remark" 
                placeholder="選填，對本次訂單的說明（建議先和商家溝通確認）"
                class="form-control"
                rows="2"
              ></textarea>
            </div>
          </div>
        </div>
        
        <!-- 商品清單 -->
        <div class="section products-section">
          <h2 class="section-title">商品清單</h2>
          <div class="product-list">
            <div 
              v-for="item in selectedItems" 
              :key="item.id" 
              class="product-item"
            >
              <div class="product-image">
                <img :src="item.productImageUrl || '/default-product.jpg'" :alt="item.productName" />
              </div>
              <div class="product-info">
                <h3 class="product-name">{{ item.productName }}</h3>
                <p class="product-price">¥{{ item.price }}</p>
              </div>
              <div class="product-quantity">
                x{{ item.quantity }}
              </div>
              <div class="product-subtotal">
                ¥{{ (item.price * item.quantity).toFixed(2) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 支付方式 -->
        <div class="section payment-section">
          <h2 class="section-title">支付方式</h2>
          <div class="payment-methods">
            <label class="payment-method">
              <input 
                type="radio" 
                value="alipay" 
                v-model="orderForm.paymentMethod"
              />
              <span class="payment-icon">💰</span>
              支付寶
            </label>
          </div>
        </div>
        
        <!-- 訂單摘要 -->
        <div class="section summary-section">
          <h2 class="section-title">訂單摘要</h2>
          <div class="summary-details">
            <div class="summary-row">
              <span>商品總數：</span>
              <span>{{ totalQuantity }} 件</span>
            </div>
            <div class="summary-row">
              <span>商品總價：</span>
              <span>¥{{ totalAmount.toFixed(2) }}</span>
            </div>
            <div class="summary-row">
              <span>運費：</span>
              <span>免運費</span>
            </div>
            <div class="summary-row total-row">
              <span>應付總額：</span>
              <span class="total-amount">¥{{ totalAmount.toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作欄 -->
      <div class="checkout-footer">
        <div class="footer-summary">
          <span class="item-count">共 {{ totalQuantity }} 件商品</span>
          <span class="total-amount">合計：¥{{ totalAmount.toFixed(2) }}</span>
        </div>
        <div class="footer-actions">
          <button class="btn btn-secondary" @click="goBack">返回購物車</button>
          <button 
            class="btn btn-primary btn-lg" 
            @click="submitOrder"
            :disabled="!canSubmit || submitting"
          >
            {{ submitting ? '提交中...' : '提交訂單' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

interface CartItem {
  id: number
  productId: number
  productName: string
  productImageUrl: string
  price: number
  quantity: number
  selected: boolean
}

interface OrderForm {
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  remark: string
  paymentMethod: string
}

const router = useRouter()
const selectedItems = ref<CartItem[]>([])
const submitting = ref(false)

const orderForm = ref<OrderForm>({
  receiverName: '',
  receiverPhone: '',
  receiverAddress: '',
  remark: '',
  paymentMethod: 'alipay'
})

// 計算屬性
const totalQuantity = computed(() => 
  selectedItems.value.reduce((total, item) => total + item.quantity, 0)
)

const totalAmount = computed(() => 
  selectedItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
)

const canSubmit = computed(() => 
  orderForm.value.receiverName.trim() !== '' &&
  orderForm.value.receiverPhone.trim() !== '' &&
  orderForm.value.receiverAddress.trim() !== '' &&
  selectedItems.value.length > 0
)

// 方法
const loadSelectedItems = async () => {
  try {
    const response = await fetch('/api/cart/selected', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        selectedItems.value = result.data
      }
    }
    
    if (selectedItems.value.length === 0) {
      ElMessage.warning('沒有選中的商品')
      router.push('/cart')
    }
  } catch (error) {
    console.error('加載選中商品失敗:', error)
    ElMessage.error('加載商品信息失敗')
    router.push('/cart')
  }
}

const submitOrder = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('請填寫完整的收貨信息')
    return
  }
  
  try {
    submitting.value = true
    
    const response = await fetch('/api/orders/create-from-cart', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: new URLSearchParams({
        receiverName: orderForm.value.receiverName,
        receiverPhone: orderForm.value.receiverPhone,
        receiverAddress: orderForm.value.receiverAddress,
        remark: orderForm.value.remark || ''
      })
    })
    
    const result = await response.json()
    
    if (result.success && result.data) {
      ElMessage.success('訂單創建成功')
      // 跳轉到支付頁面
      router.push(`/payment/${result.data.id}`)
    } else {
      ElMessage.error(result.message || '創建訂單失敗')
    }
  } catch (error) {
    console.error('提交訂單失敗:', error)
    ElMessage.error('提交訂單失敗')
  } finally {
    submitting.value = false
  }
}

const goBack = () => {
  router.push('/cart')
}

onMounted(() => {
  loadSelectedItems()
})
</script>

<style scoped>
.checkout-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.checkout-content {
  margin-bottom: 80px;
}

.section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-group {
  flex: 1;
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.product-info {
  flex: 1;
  margin-left: 15px;
}

.product-name {
  font-size: 16px;
  margin-bottom: 5px;
}

.product-price {
  color: #e74c3c;
  font-weight: bold;
}

.product-quantity {
  margin: 0 20px;
  color: #666;
}

.product-subtotal {
  font-weight: bold;
  color: #e74c3c;
}

.payment-methods {
  display: flex;
  gap: 20px;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.payment-method:hover {
  border-color: #007bff;
}

.payment-method input[type="radio"] {
  margin-right: 10px;
}

.payment-icon {
  margin-right: 5px;
  font-size: 18px;
}

.summary-details {
  max-width: 400px;
  margin-left: auto;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.total-row {
  border-top: 2px solid #eee;
  padding-top: 15px;
  margin-top: 15px;
  font-size: 18px;
  font-weight: bold;
}

.total-amount {
  color: #e74c3c;
}

.checkout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #eee;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
}

.footer-summary {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
