<template>
  <div class="product-detail-view">
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首頁</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/products' }">商品列表</el-breadcrumb-item>
        <el-breadcrumb-item>商品詳情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <div class="page-content">
      <ProductDetail 
        :product-id="productId"
        @product-select="handleProductSelect"
        @buy-now="handleBuyNow"
        @add-to-cart="handleAddToCart"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import ProductDetail from '../components/ProductDetail.vue'
import type { Product } from '../api/product'

// Router
const route = useRoute()
const router = useRouter()

// 計算屬性
const productId = computed(() => {
  const id = route.params.id
  return typeof id === 'string' ? parseInt(id, 10) : 0
})

// 處理商品選擇
const handleProductSelect = (product: Product) => {
  router.push(`/products/${product.id}`)
}

// 處理立即購買
const handleBuyNow = (product: Product, quantity: number) => {
  ElMessage.info(`立即購買 ${product.name} x ${quantity}`)
  // 這裡可以實現跳轉到結算頁面的邏輯
}

// 處理加入購物車
const handleAddToCart = async (product: Product, quantity: number) => {
  try {
    const response = await fetch('/api/cart/add', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: new URLSearchParams({
        productId: product.id.toString(),
        quantity: quantity.toString()
      })
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        ElMessage.success(`已加入購物車: ${product.name} x ${quantity}`)
      } else {
        ElMessage.error(result.message || '加入購物車失敗')
      }
    } else {
      ElMessage.error('加入購物車失敗')
    }
  } catch (error) {
    console.error('加入購物車失敗:', error)
    ElMessage.error('加入購物車失敗')
  }
}

// 組件掛載時檢查商品ID
onMounted(() => {
  if (!productId.value) {
    ElMessage.error('無效的商品ID')
    router.push('/products')
  }
})
</script>

<style scoped>
.product-detail-view {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}
</style>
