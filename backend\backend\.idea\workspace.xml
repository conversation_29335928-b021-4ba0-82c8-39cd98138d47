<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="79954498-fe6e-4835-b44a-ec2390811f6f" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/CoolRequestSetting.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/example/Main.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zf3anv42Oq074ejcWR2PFAQBBM" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.Main.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="Main" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.Main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="79954498-fe6e-4835-b44a-ec2390811f6f" name="Changes" comment="" />
      <created>1752108367876</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752108367876</updated>
      <workItem from="1752108383189" duration="15894000" />
      <workItem from="1752151968794" duration="697000" />
      <workItem from="1752472361153" duration="1235000" />
      <workItem from="1752481695385" duration="668000" />
      <workItem from="1752540441621" duration="5717000" />
      <workItem from="1752627174478" duration="2820000" />
      <workItem from="1752732555323" duration="2061000" />
      <workItem from="1752744643686" duration="784000" />
      <workItem from="1752797883160" duration="3189000" />
      <workItem from="1753273774278" duration="2036000" />
      <workItem from="1753316901615" duration="3491000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>