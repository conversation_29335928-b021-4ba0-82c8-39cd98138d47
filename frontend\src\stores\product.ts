import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { productAPI, productCategoryAPI, type Product, type ProductCategory, type PagedResponse } from '../api/product'
import { ElMessage } from 'element-plus'

export const useProductStore = defineStore('product', () => {
  // 狀態
  const products = ref<Product[]>([])
  const currentProduct = ref<Product | null>(null)
  const categories = ref<ProductCategory[]>([])
  const categoryTree = ref<ProductCategory[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 分頁信息
  const pagination = ref({
    page: 0,
    size: 20,
    totalElements: 0,
    totalPages: 0,
    first: true,
    last: false
  })
  
  // 搜索和篩選條件
  const searchKeyword = ref('')
  const selectedCategoryId = ref<number | null>(null)
  const priceRange = ref<[number, number]>([0, 999999])
  const selectedBrand = ref('')
  const showRecommendedOnly = ref(false)
  const showHotOnly = ref(false)
  
  // 計算屬性
  const hasProducts = computed(() => products.value.length > 0)
  const isFirstPage = computed(() => pagination.value.first)
  const isLastPage = computed(() => pagination.value.last)
  const totalProducts = computed(() => pagination.value.totalElements)
  
  // 獲取分類樹
  const loadCategoryTree = async () => {
    try {
      loading.value = true
      const response = await productCategoryAPI.getCategoryTree()
      if (response.success && response.data) {
        categoryTree.value = response.data
      } else {
        throw new Error(response.message || '獲取分類樹失敗')
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('獲取分類樹失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }
  
  // 獲取葉子分類
  const loadLeafCategories = async () => {
    try {
      const response = await productCategoryAPI.getLeafCategories()
      if (response.success && response.data) {
        categories.value = response.data
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('獲取分類列表失敗: ' + err.message)
    }
  }
  
  // 分頁查詢商品
  const loadProducts = async (page = 0, size = 20) => {
    try {
      loading.value = true
      error.value = null
      
      let response
      if (selectedCategoryId.value) {
        response = await productAPI.getProductsByCategory(selectedCategoryId.value, page, size)
      } else {
        response = await productAPI.getProducts(page, size)
      }
      
      if (response.success && response.data) {
        products.value = response.data.content
        updatePagination(response.data)
      } else {
        throw new Error(response.message || '獲取商品列表失敗')
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('獲取商品列表失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }
  
  // 搜索商品
  const searchProducts = async (keyword: string, page = 0, size = 20) => {
    try {
      loading.value = true
      error.value = null
      searchKeyword.value = keyword
      
      const response = await productAPI.searchProducts(keyword, page, size)
      if (response.success && response.data) {
        products.value = response.data.content
        updatePagination(response.data)
      } else {
        throw new Error(response.message || '搜索商品失敗')
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('搜索商品失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }
  
  // 多條件篩選商品
  const filterProducts = async (page = 0, size = 20) => {
    try {
      loading.value = true
      error.value = null
      
      const params = {
        name: searchKeyword.value || undefined,
        categoryId: selectedCategoryId.value || undefined,
        minPrice: priceRange.value[0] > 0 ? priceRange.value[0] : undefined,
        maxPrice: priceRange.value[1] < 999999 ? priceRange.value[1] : undefined,
        brand: selectedBrand.value || undefined,
        isRecommended: showRecommendedOnly.value ? 1 : undefined,
        isHot: showHotOnly.value ? 1 : undefined,
        page,
        size
      }
      
      const response = await productAPI.searchProductsWithFilters(params)
      if (response.success && response.data) {
        products.value = response.data.content
        updatePagination(response.data)
      } else {
        throw new Error(response.message || '篩選商品失敗')
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('篩選商品失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }
  
  // 獲取推薦商品
  const loadRecommendedProducts = async (page = 0, size = 20) => {
    try {
      loading.value = true
      const response = await productAPI.getRecommendedProducts(page, size)
      if (response.success && response.data) {
        products.value = response.data.content
        updatePagination(response.data)
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('獲取推薦商品失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }
  
  // 獲取熱門商品
  const loadHotProducts = async (page = 0, size = 20) => {
    try {
      loading.value = true
      const response = await productAPI.getHotProducts(page, size)
      if (response.success && response.data) {
        products.value = response.data.content
        updatePagination(response.data)
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('獲取熱門商品失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }

  // 按價格排序獲取商品
  const getProductsByPrice = async (ascending = true, page = 0, size = 20) => {
    try {
      loading.value = true
      error.value = null

      const response = await productAPI.getProductsByPrice(ascending, page, size)
      if (response.success && response.data) {
        products.value = response.data.content
        updatePagination(response.data)
      } else {
        throw new Error(response.message || '按價格排序獲取商品失敗')
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('按價格排序獲取商品失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }

  // 獲取銷量最好的商品
  const getBestSellingProducts = async (page = 0, size = 20) => {
    try {
      loading.value = true
      error.value = null

      const response = await productAPI.getBestSellingProducts(page, size)
      if (response.success && response.data) {
        products.value = response.data.content
        updatePagination(response.data)
      } else {
        throw new Error(response.message || '獲取銷量排序商品失敗')
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('獲取銷量排序商品失敗: ' + err.message)
    } finally {
      loading.value = false
    }
  }
  
  // 獲取商品詳情
  const loadProductDetail = async (id: number) => {
    try {
      loading.value = true
      const response = await productAPI.getProductById(id)
      if (response.success && response.data) {
        currentProduct.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '獲取商品詳情失敗')
      }
    } catch (err: any) {
      error.value = err.message
      ElMessage.error('獲取商品詳情失敗: ' + err.message)
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 獲取相關商品
  const loadRelatedProducts = async (productId: number, limit = 10) => {
    try {
      const response = await productAPI.getRelatedProducts(productId, limit)
      if (response.success && response.data) {
        return response.data
      }
      return []
    } catch (err: any) {
      ElMessage.error('獲取相關商品失敗: ' + err.message)
      return []
    }
  }
  
  // 更新分頁信息
  const updatePagination = (data: PagedResponse<Product>) => {
    pagination.value = {
      page: data.number,
      size: data.size,
      totalElements: data.totalElements,
      totalPages: data.totalPages,
      first: data.first,
      last: data.last
    }
  }
  
  // 設置分類篩選
  const setCategoryFilter = (categoryId: number | null) => {
    selectedCategoryId.value = categoryId
  }
  
  // 設置價格範圍
  const setPriceRange = (range: [number, number]) => {
    priceRange.value = range
  }
  
  // 設置品牌篩選
  const setBrandFilter = (brand: string) => {
    selectedBrand.value = brand
  }
  
  // 切換推薦篩選
  const toggleRecommendedFilter = () => {
    showRecommendedOnly.value = !showRecommendedOnly.value
  }
  
  // 切換熱門篩選
  const toggleHotFilter = () => {
    showHotOnly.value = !showHotOnly.value
  }
  
  // 清除篩選條件
  const clearFilters = () => {
    searchKeyword.value = ''
    selectedCategoryId.value = null
    priceRange.value = [0, 999999]
    selectedBrand.value = ''
    showRecommendedOnly.value = false
    showHotOnly.value = false
  }
  
  // 重置狀態
  const reset = () => {
    products.value = []
    currentProduct.value = null
    categories.value = []
    categoryTree.value = []
    loading.value = false
    error.value = null
    clearFilters()
    pagination.value = {
      page: 0,
      size: 20,
      totalElements: 0,
      totalPages: 0,
      first: true,
      last: false
    }
  }
  
  return {
    // 狀態
    products,
    currentProduct,
    categories,
    categoryTree,
    loading,
    error,
    pagination,
    searchKeyword,
    selectedCategoryId,
    priceRange,
    selectedBrand,
    showRecommendedOnly,
    showHotOnly,
    
    // 計算屬性
    hasProducts,
    isFirstPage,
    isLastPage,
    totalProducts,
    
    // 方法
    loadCategoryTree,
    loadLeafCategories,
    loadProducts,
    searchProducts,
    filterProducts,
    loadRecommendedProducts,
    loadHotProducts,
    getProductsByPrice,
    getBestSellingProducts,
    loadProductDetail,
    loadRelatedProducts,
    setCategoryFilter,
    setPriceRange,
    setBrandFilter,
    toggleRecommendedFilter,
    toggleHotFilter,
    clearFilters,
    reset
  }
})
